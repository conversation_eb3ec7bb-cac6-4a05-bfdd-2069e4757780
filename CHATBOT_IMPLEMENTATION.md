# Performance Insights Bot Implementation

## Overview

The Performance Insights Bot is a question-answering chatbot integrated into the Student Management System. It allows students to ask questions about their academic performance, subjects, and grades, and receive intelligent responses based on a pre-trained NLP model.

## Architecture

The bot uses a Java-native approach with Deep Java Library (DJL) to integrate HuggingFace transformer models directly into the Spring Boot backend, eliminating the need for a separate Python microservice.

### Components

1. **Frontend**: Angular component with chat interface
2. **Backend**: Spring Boot REST controller and service
3. **NLP Model**: HuggingFace BERT-base-cased-squad2 model via DJL

## Implementation Details

### Backend Implementation

#### Dependencies

The following DJL dependencies were added to `pom.xml`:

```xml
<dependency>
    <groupId>ai.djl</groupId>
    <artifactId>api</artifactId>
    <version>0.33.0</version>
</dependency>
<dependency>
    <groupId>ai.djl.pytorch</groupId>
    <artifactId>pytorch-engine</artifactId>
    <version>0.33.0</version>
</dependency>
<dependency>
    <groupId>ai.djl.huggingface</groupId>
    <artifactId>tokenizers</artifactId>
    <version>0.33.0</version>
</dependency>
```

#### QuestionAnsweringService

Located at: `src/main/java/org/example/studentmanagement/service/chatbot/QuestionAnsweringService.java`

This service:
- Loads the HuggingFace BERT-base-cased-squad2 model using DJL
- Provides an `answerQuestion` method that takes a question and optional context
- Handles model initialization and cleanup

#### ChatbotController

Located at: `src/main/java/org/example/studentmanagement/controller/ChatbotController.java`

This controller:
- Exposes a `/api/chatbot/ask` POST endpoint
- Secures the endpoint with `@PreAuthorize("hasRole('STUDENT') or hasRole('ADMIN')")`
- Accepts JSON requests with `question` and optional `context` fields
- Returns JSON responses with the answer from the model

### Frontend Implementation

#### PerformanceInsightsComponent

Located at: `src/app/components/chatbot/performance-insights/`

This component includes:
- TypeScript logic for handling user input and API communication
- HTML template for the chat interface
- SCSS styling for a modern chat UI

#### Routing

The component is registered in:
- `app.module.ts` (imported and declared)
- `app-routing.module.ts` (routed at `/performance-insights`)

#### Navigation

A link to the chatbot was added to the student navigation in `navigation.component.html`.

## Prerequisites for Running

To run the complete application, you need:

1. **Java 11 or higher**
2. **Maven 3.6 or higher**
3. **Node.js 16 or higher**
4. **npm or yarn**

## Setup Instructions

### Backend Setup

1. Ensure Java and Maven are installed and in your PATH
2. Navigate to the project root directory
3. Build the project:
   ```bash
   mvn clean install
   ```
4. Run the Spring Boot application:
   ```bash
   mvn spring-boot:run
   ```
   Or:
   ```bash
   java -jar target/*.jar
   ```

### Frontend Setup

1. Ensure Node.js and npm are installed
2. Navigate to the frontend directory (same as backend in this project)
3. Install dependencies:
   ```bash
   npm install
   ```
4. Start the development server:
   ```bash
   npm start
   ```
5. Open your browser to `http://localhost:4200`

## API Endpoints

### Ask Question

- **URL**: `/api/chatbot/ask`
- **Method**: `POST`
- **Authentication**: Required (STUDENT or ADMIN role)
- **Request Body**:
  ```json
  {
    "question": "string",
    "context": "string (optional)"
  }
  ```
- **Response**:
  ```json
  {
    "answer": "string",
    "question": "string"
  }
  ```

## Testing

To test the chatbot functionality:

1. Start both the backend and frontend servers
2. Log in as a student user
3. Navigate to the "Performance Insights" section
4. Ask questions about academic performance, subjects, or grades

Example questions:
- "What is my average grade in Mathematics?"
- "Which subject do I need to improve in?"
- "How are my grades compared to last semester?"

## Security

The chatbot endpoint is secured with Spring Security:
- Only authenticated users can access the endpoint
- Only users with STUDENT or ADMIN roles can use the chatbot
- CORS is enabled for the Angular frontend

## Model Information

The implementation uses the HuggingFace BERT-base-cased-squad2 model:
- Model URL: `https://mlrepo.djl.ai/model/nlp/question_answer/ai/djl/huggingface/pytorch/deepset/bert-base-cased-squad2/0.0.1/bert-base-cased-squad2.zip`
- The model is automatically downloaded on first use
- The model is optimized for question-answering tasks

## Limitations

1. The model requires an internet connection for initial download
2. Model performance depends on the quality of the context provided
3. The bot is designed for educational Q&A and may not handle general conversation well

## Future Enhancements

1. Add context-awareness using student-specific data
2. Implement rate limiting for the chatbot endpoint
3. Add logging and analytics for chatbot usage
4. Improve error handling and user feedback
5. Add support for follow-up questions
6. Implement caching for common questions
