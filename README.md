# Student Management System - Frontend

A modern Angular frontend for the Student Management System with a beautiful, intuitive UI design.

## Features

- **Modern UI/UX**: Clean, responsive design using Angular Material
- **Role-based Access**: Different interfaces for students and administrators
- **Authentication**: Secure login/register system with JWT tokens
- **Dashboard**: Overview with statistics and recent activities
- **Student Management**: View and manage student information
- **Subject Management**: Manage academic subjects
- **Grade Management**: Record and view student grades
- **Responsive Design**: Works on desktop, tablet, and mobile devices

## Prerequisites

- Node.js (version 16 or higher)
- npm or yarn
- Angular CLI (`npm install -g @angular/cli`)

## Installation

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Start the development server:**
   ```bash
   npm start
   ```

3. **Open your browser:**
   Navigate to `http://localhost:4200`

## Project Structure

```
src/
├── app/
│   ├── components/
│   │   ├── auth/
│   │   │   ├── login/
│   │   │   └── register/
│   │   ├── dashboard/
│   │   ├── students/
│   │   ├── subjects/
│   │   ├── grades/
│   │   ├── navigation/
│   │   └── profile/
│   ├── models/
│   ├── services/
│   ├── guards/
│   └── interceptors/
├── assets/
└── styles/
```

## Key Components

### Authentication
- **Login Component**: User authentication with form validation
- **Register Component**: New user registration with role selection
- **Auth Service**: Handles authentication logic and token management

### Dashboard
- **Dashboard Component**: Overview with statistics and recent activities
- **Navigation Component**: Responsive navigation with role-based menu items

### Data Management
- **Student Service**: CRUD operations for student data
- **Subject Service**: Manage academic subjects
- **Grade Service**: Handle grade recording and calculations

## API Integration

The frontend connects to your Spring Boot backend at `http://localhost:8080` with the following endpoints:

- **Authentication**: `/api/v1/auth`
- **Students**: `/api/v1/etudiants`
- **Subjects**: `/api/v1/matieres`
- **Grades**: `/api/v1/notes`

## User Roles

### Student Role (`ROLE_STUDENT`)
- View personal dashboard
- Access personal grades
- View profile information

### Admin Role (`ROLE_ADMIN`)
- Full access to all features
- Manage students, subjects, and grades
- View system statistics
- Access administrative functions

## Styling

The application uses:
- **Angular Material**: For UI components
- **SCSS**: For custom styling
- **CSS Variables**: For consistent theming
- **Responsive Design**: Mobile-first approach

## Development

### Available Scripts

- `npm start`: Start development server
- `npm run build`: Build for production
- `npm run test`: Run unit tests
- `npm run lint`: Run linting

### Code Style

The project follows Angular best practices:
- Component-based architecture
- Reactive forms for data input
- Service-based data management
- Route guards for security
- HTTP interceptors for authentication

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## Troubleshooting

### Common Issues

1. **CORS Errors**: Ensure your backend has CORS configured for `http://localhost:4200`
2. **Authentication Issues**: Check that JWT tokens are being sent correctly
3. **API Connection**: Verify your backend is running on port 8080

### Getting Help

If you encounter issues:
1. Check the browser console for errors
2. Verify all dependencies are installed
3. Ensure the backend is running and accessible
4. Check network connectivity

## Contributing

1. Follow Angular style guide
2. Write unit tests for new features
3. Ensure responsive design works on all screen sizes
4. Test with different user roles

## License

This project is part of the Student Management System. 

Great question! Here's how to run both backend and frontend simultaneously in IntelliJ:

## 🚀 **Method 1: Using IntelliJ Run Configurations (Recommended)**

### **Step 1: Create Backend Run Configuration**

1. **Go to Run Configurations:**
   - Click `Run` → `Edit Configurations...`
   - Or click the dropdown next to the play button

2. **Add Spring Boot Configuration:**
   - Click `+` → `Spring Boot`
   - Configure:
     ```
     Name: StudentManagement Backend
     Main class: org.example.studentmanagement.StudentManagementApplication
     JRE: Your Java version (e.g., 17)
     Working directory: Your project root
     ```

3. **Save the configuration**

### **Step 2: Create Frontend Run Configuration**

1. **Add npm Configuration:**
   - Click `+` → `npm`
   - Configure:
     ```
     Name: Angular Frontend
     package.json: Select your package.json file
     Command: run
     Scripts: start
     Node interpreter: Your Node.js installation
     ```

2. **Save the configuration**

### **Step 3: Run Both Simultaneously**

1. **Select both configurations:**
   - Hold `Ctrl` (Windows) or `Cmd` (Mac)
   - Select both "StudentManagement Backend" and "Angular Frontend"

2. **Run both:**
   - Click the green play button
   - Both will start simultaneously

## 🎯 **Method 2: Using IntelliJ Terminal**

### **Step 1: Start Backend**
1. **Open Terminal in IntelliJ:**
   - `View` → `Tool Windows` → `Terminal`
   - Or `Alt + F12` (Windows) / `⌘ + F12` (Mac)

2. **Run Spring Boot:**
   ```bash
   mvn spring-boot:run
   ```

### **Step 2: Start Frontend (New Terminal Tab)**
1. **Open new terminal tab:**
   - Click `+` in terminal window

2. **Run Angular:**
   ```bash
   npm start
   ```

## 🔧 **Method 3: Using Maven + Terminal**

### **Step 1: Start Backend with Maven**
```bash
# In IntelliJ terminal
mvn spring-boot:run
```

### **Step 2: Start Frontend**
```bash
# In new terminal tab
npm start
```

## 📋 **Complete Setup Guide**

### **1. Verify Your Setup:**
```bash
# Check if you're in the right directory
pwd
# Should show: C:\Users\<USER>\Downloads\StudentManagement

# Check if files exist
ls pom.xml
ls package.json
```

### **2. Start Backend First:**
```bash
# Method A: Using Maven
mvn spring-boot:run

# Method B: Using IntelliJ Run Configuration
# (Click play button next to StudentManagementApplication.java)
```

### **3. Start Frontend:**
```bash
# In new terminal tab
npm start
```

## 🎉 **Expected Results:**

- **Backend**: `http://localhost:8080` ✅
- **Frontend**: `http://localhost:4200` ✅
- **Database**: MySQL running ✅

## 🔍 **Troubleshooting:**

### **If Backend Won't Start:**
1. Check database connection in `application.properties`
2. Ensure MySQL is running
3. Check IntelliJ console for errors

### **If Frontend Won't Start:**
1. Ensure you're in the correct directory
2. Run `npm install` if needed
3. Check terminal for compilation errors

### **If Ports Are Busy:**
```bash
# Check what's using port 8080
netstat -ano | findstr :8080

# Kill process if needed (replace PID)
taskkill /PID <PID> /F
```

## 🎉 **Pro Tips:**

1. **Use IntelliJ Run Configurations** - It's the most professional way
2. **Start Backend First** - Frontend needs backend to be running
3. **Check Both Consoles** - Monitor both for errors
4. **Use Different Terminal Tabs** - Keep them organized

## 📱 **Quick Commands:**

```bash
# Backend
mvn spring-boot:run

# Frontend  
npm start

# Both (in separate terminals)
mvn spring-boot:run & npm start
```

This way you can develop both frontend and backend simultaneously with hot reloading! 🚀 