# Exam and Class Session Management Features

This document describes the new Exam and ClassSession entities added to the Student Management System.

## Overview

The system now includes comprehensive management of exams and class sessions with the following features:

- **Admin Management**: Full CRUD operations for both exams and class sessions
- **Student Calendar View**: Weekly calendar showing exams and class sessions
- **Validation**: Time overlap prevention and business rule enforcement
- **Role-based Access**: Different permissions for admins and students

## Backend Implementation

### Entities

#### Exam Entity
```java
@Entity
@Table(name = "exams")
public class Exam {
    private Long id;
    private String title;
    private Matiere subject; // Many-to-One relationship
    private LocalDate date;
    private LocalTime startTime;
    private LocalTime endTime;
    private String location;
    private List<Etudiant> students; // Many-to-Many relationship
}
```

#### ClassSession Entity
```java
@Entity
@Table(name = "class_sessions")
public class ClassSession {
    private Long id;
    private Matiere subject; // Many-to-One relationship
    private String instructor;
    private DayOfWeek dayOfWeek;
    private LocalTime startTime;
    private LocalTime endTime;
    private String room;
    private List<Etudiant> students; // Many-to-Many relationship
}
```

### Key Features

1. **Time Overlap Prevention**: 
   - Exams cannot overlap in the same location
   - Class sessions cannot overlap in the same room on the same day
   - Validation ensures end time is after start time

2. **Business Rules**:
   - Class sessions cannot be scheduled on weekends
   - Exams and class sessions are associated with subjects and students

3. **Repository Methods**:
   - Find by student ID
   - Find by date range
   - Find by subject
   - Find overlapping events

### REST Endpoints

#### Exam Endpoints
- `GET /api/exams` - Get all exams (Admin only)
- `GET /api/exams/{id}` - Get exam by ID (Admin only)
- `POST /api/exams` - Create exam (Admin only)
- `PUT /api/exams/{id}` - Update exam (Admin only)
- `DELETE /api/exams/{id}` - Delete exam (Admin only)
- `GET /api/exams/student/{studentId}` - Get exams for student (Student only)
- `GET /api/exams/student/{studentId}/date-range` - Get exams by date range (Student only)

#### ClassSession Endpoints
- `GET /api/class-sessions` - Get all class sessions (Admin only)
- `GET /api/class-sessions/{id}` - Get class session by ID (Admin only)
- `POST /api/class-sessions` - Create class session (Admin only)
- `PUT /api/class-sessions/{id}` - Update class session (Admin only)
- `DELETE /api/class-sessions/{id}` - Delete class session (Admin only)
- `GET /api/class-sessions/student/{studentId}` - Get class sessions for student (Student only)
- `GET /api/class-sessions/student/{studentId}/day/{dayOfWeek}` - Get class sessions by day (Student only)

## Frontend Implementation

### Components

#### Admin Components
1. **AdminExamManagementComponent**
   - Full CRUD operations for exams
   - Table view with sorting and filtering
   - Dialog forms for create/edit operations
   - Student assignment functionality

2. **AdminClassSessionManagementComponent**
   - Full CRUD operations for class sessions
   - Table view with day-of-week display
   - Room and time validation

#### Student Components
1. **StudentCalendarComponent**
   - Weekly calendar view
   - Visual differentiation between exams (red) and classes (blue)
   - Event details on click
   - Navigation between weeks
   - Today button for quick navigation

### Key Features

1. **Calendar Interface**:
   - Weekly view with time slots
   - Color-coded events (exams vs classes)
   - Hover effects and tooltips
   - Responsive design

2. **Form Validation**:
   - Required field validation
   - Time format validation
   - Date picker integration
   - Student selection with checkboxes

3. **User Experience**:
   - Loading states
   - Success/error notifications
   - Confirmation dialogs for deletions
   - Responsive tables

### Navigation

New menu items have been added:
- **Admin**: "Exams" menu item for exam management
- **Student**: "Calendar" menu item for calendar view

## Database Schema

### New Tables
```sql
-- Exams table
CREATE TABLE exams (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    subject_id BIGINT NOT NULL,
    exam_date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    location VARCHAR(255) NOT NULL,
    FOREIGN KEY (subject_id) REFERENCES matieres(id)
);

-- Class sessions table
CREATE TABLE class_sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    subject_id BIGINT NOT NULL,
    instructor VARCHAR(255) NOT NULL,
    day_of_week VARCHAR(20) NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    room VARCHAR(255) NOT NULL,
    FOREIGN KEY (subject_id) REFERENCES matieres(id)
);

-- Junction tables for many-to-many relationships
CREATE TABLE exam_students (
    exam_id BIGINT,
    student_id BIGINT,
    PRIMARY KEY (exam_id, student_id),
    FOREIGN KEY (exam_id) REFERENCES exams(id),
    FOREIGN KEY (student_id) REFERENCES etudiants(id)
);

CREATE TABLE class_session_students (
    class_session_id BIGINT,
    student_id BIGINT,
    PRIMARY KEY (class_session_id, student_id),
    FOREIGN KEY (class_session_id) REFERENCES class_sessions(id),
    FOREIGN KEY (student_id) REFERENCES etudiants(id)
);
```

## Usage Instructions

### For Administrators

1. **Managing Exams**:
   - Navigate to "Exams" in the admin menu
   - Click "Add New Exam" to create exams
   - Fill in title, subject, date, times, location, and select students
   - Use edit/delete buttons for existing exams

2. **Managing Class Sessions**:
   - Navigate to "Class Sessions" in the admin menu
   - Create recurring class sessions with day-of-week scheduling
   - Assign instructors and rooms
   - Select students for each session

### For Students

1. **Viewing Calendar**:
   - Navigate to "Calendar" in the student menu
   - View weekly schedule with exams and classes
   - Click on events for detailed information
   - Use navigation buttons to move between weeks

## Technical Notes

### Time Zone Handling
- All times are stored in the database as local times
- The frontend displays times in the user's local timezone
- Date pickers use the browser's locale settings

### Validation Rules
- Exam end time must be after start time
- Class session end time must be after start time
- No overlapping exams in the same location
- No overlapping class sessions in the same room on the same day
- Class sessions cannot be on weekends

### Security
- All endpoints are protected with role-based authentication
- Admins have full CRUD access
- Students have read-only access to their own data
- JWT tokens are used for authentication

## Future Enhancements

Potential improvements for future versions:
1. Monthly calendar view
2. Export calendar to iCal format
3. Email notifications for upcoming exams
4. Room booking system
5. Conflict resolution suggestions
6. Recurring exam patterns
7. Calendar sharing between students
8. Mobile-responsive calendar interface 