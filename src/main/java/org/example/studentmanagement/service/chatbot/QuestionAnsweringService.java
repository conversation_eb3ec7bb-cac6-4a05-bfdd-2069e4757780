package org.example.studentmanagement.service.chatbot;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

@Service
public class QuestionAnsweringService {

    private static final Logger logger = LoggerFactory.getLogger(QuestionAnsweringService.class);

    private final Map<Pattern, String> responsePatterns;

    public QuestionAnsweringService() {
        this.responsePatterns = initializeResponsePatterns();
    }

    private Map<Pattern, String> initializeResponsePatterns() {
        Map<Pattern, String> patterns = new HashMap<>();

        // Greeting patterns
        patterns.put(Pattern.compile("(?i).*(hello|hi|hey|good morning|good afternoon).*"),
                    "Hello! I'm your Performance Insights Bot. I can help you with questions about your academic performance, grades, subjects, and study guidance.");

        // Grade-related questions
        patterns.put(Pattern.compile("(?i).*(grade|mark|score|result).*"),
                    "I can help you understand your academic performance. To view your specific grades, please check the 'My Grades' section in your dashboard. Your grades are calculated based on your exam scores and assignments.");

        // Subject-related questions
        patterns.put(Pattern.compile("(?i).*(subject|course|class|module).*"),
                    "You can find information about your subjects in the system. Each subject has its own grades, assignments, and performance metrics. Check your dashboard for subject-specific details.");

        // Performance and improvement
        patterns.put(Pattern.compile("(?i).*(improve|better|study|help|advice).*"),
                    "Here are some tips to improve your academic performance: 1) Review your grades regularly, 2) Focus on subjects where you're scoring lower, 3) Ask your teachers for feedback, 4) Create a study schedule, 5) Use the performance insights in your dashboard to track progress.");

        // Average/GPA questions
        patterns.put(Pattern.compile("(?i).*(average|gpa|overall|total).*"),
                    "Your overall academic performance is calculated based on all your subject grades. You can view your average scores and GPA in the 'My Grades' section. The system automatically calculates your performance metrics.");

        // Schedule/timetable questions
        patterns.put(Pattern.compile("(?i).*(schedule|timetable|when|time).*"),
                    "For schedule and timetable information, please check with your academic coordinator or the main dashboard. The system focuses on grade management and performance tracking.");

        // Thank you responses
        patterns.put(Pattern.compile("(?i).*(thank|thanks).*"),
                    "You're welcome! I'm here to help with any questions about your academic performance. Feel free to ask about your grades, subjects, or study advice anytime.");

        return patterns;
    }
    
    public String answerQuestion(String question, String context) {
        try {
            logger.info("Processing question: {}", question);

            if (question == null || question.trim().isEmpty()) {
                return "Please ask me a question about your academic performance, grades, or subjects.";
            }

            String normalizedQuestion = question.trim().toLowerCase();

            // Check for patterns and return appropriate responses
            for (Map.Entry<Pattern, String> entry : responsePatterns.entrySet()) {
                if (entry.getKey().matcher(normalizedQuestion).matches()) {
                    logger.info("Matched pattern for question: {}", question);
                    return entry.getValue();
                }
            }

            // Default response for unmatched questions
            logger.info("No specific pattern matched for question: {}", question);
            return "I understand you're asking about your academic performance. While I don't have a specific answer for that question, " +
                   "I can help you with information about your grades, subjects, study advice, and academic performance. " +
                   "You can also check your dashboard for detailed performance metrics and grades.";

        } catch (Exception e) {
            logger.error("Error while processing question: " + question, e);
            return "Sorry, I encountered an error while processing your question. Please try again.";
        }
    }
}
