package org.example.studentmanagement.service.chatbot;

import ai.djl.Application;
import ai.djl.MalformedModelException;
import ai.djl.ModelException;
import ai.djl.inference.Predictor;
import ai.djl.modality.nlp.qa.QAInput;
import ai.djl.repository.zoo.Criteria;
import ai.djl.repository.zoo.ModelNotFoundException;
import ai.djl.repository.zoo.ZooModel;
import ai.djl.translate.TranslateException;
import ai.djl.huggingface.translator.QuestionAnsweringTranslatorFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Paths;

@Service
public class QuestionAnsweringService {
    
    private static final Logger logger = LoggerFactory.getLogger(QuestionAnsweringService.class);
    
    private static final String MODEL_NAME = "deepset/bert-base-cased-squad2";
    
    private ZooModel<QAInput, String> model;
    private Predictor<QAInput, String> predictor;
    private volatile boolean modelLoaded = false;
    private final Object lock = new Object();
    
    public QuestionAnsweringService() {
        // Don't load model at startup, load it lazily when first needed
    }
    
    private void initializeModel() {
        if (modelLoaded) {
            return;
        }
        
        synchronized (lock) {
            if (modelLoaded) {
                return;
            }
            
            try {
                logger.info("Loading question answering model...");
                Criteria<QAInput, String> criteria = Criteria.builder()
                        .setTypes(QAInput.class, String.class)
                        .optApplication(Application.NLP.QUESTION_ANSWER)
                        .optEngine("PyTorch")
                        .optModelPath(Paths.get("C:\\models\\bert-base-cased-squad2"))
                        .optModelName("pytorch_model") // DJL adds ".bin"
                        .optTranslatorFactory(new QuestionAnsweringTranslatorFactory())
                        .build();



                model = criteria.loadModel();
                predictor = model.newPredictor();
                modelLoaded = true;
                
                logger.info("Question answering model loaded successfully");
            } catch (ModelNotFoundException | MalformedModelException | IOException e) {
                logger.error("Failed to load question answering model", e);
                throw new RuntimeException("Failed to load question answering model", e);
            }
        }
    }
    
    public String answerQuestion(String question, String context) {
        try {
            // Load model if not already loaded
            if (!modelLoaded) {
                initializeModel();
            }
            
            // If no context provided, use a general educational context
            if (context == null || context.trim().isEmpty()) {
                context = "This is a student management system for educational institutions. " +
                         "Students can ask questions about their academic performance, grades, subjects, " +
                         "schedules, and other educational matters. The system helps students understand " +
                         "their academic progress and provides guidance for improvement.";
            }
            
            QAInput input = new QAInput(question, context);
            return predictor.predict(input);
        } catch (TranslateException e) {
            logger.error("Failed to answer question: " + question, e);
            return "Sorry, I encountered an error while processing your question.";
        }
    }
    
    public void close() {
        if (predictor != null) {
            predictor.close();
        }
        if (model != null) {
            model.close();
        }
    }
}
