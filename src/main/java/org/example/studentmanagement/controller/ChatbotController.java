package org.example.studentmanagement.controller;

import org.example.studentmanagement.service.chatbot.QuestionAnsweringService;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.security.Principal;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/chatbot")
@CrossOrigin(origins = "http://localhost:4200")
public class ChatbotController {
    
    private final QuestionAnsweringService questionAnsweringService;
    
    public ChatbotController(QuestionAnsweringService questionAnsweringService) {
        this.questionAnsweringService = questionAnsweringService;
    }
    
    @PostMapping("/ask")
    @PreAuthorize("hasRole('STUDENT') or hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> askQuestion(@RequestBody Map<String, String> request, Principal principal) {
        String question = request.get("question");
        String context = request.get("context"); // Optional
        
        if (question == null || question.trim().isEmpty()) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "Question is required");
            return ResponseEntity.badRequest().body(errorResponse);
        }
        
        try {
            String answer = questionAnsweringService.answerQuestion(question, context);
            Map<String, Object> response = new HashMap<>();
            response.put("answer", answer);
            response.put("question", question);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "Failed to process question: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
}
