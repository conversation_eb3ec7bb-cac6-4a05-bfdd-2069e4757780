 spring.datasource.url=*************************************
spring.datasource.username=root
spring.datasource.password=
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.show-sql=true
jwt.secret=W3j9kX2mQzRtYvU7nFsA3eDbHcG4jKpN6

spring.h2.console.enabled=false
logging.level.org.springframework=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
logging.level.org.hibernate.engine.jdbc.spi=DEBUG

# Add more detailed logging for authentication
logging.level.org.example.studentmanagement=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.web=DEBUG

# Chatbot specific logging
logging.level.org.example.studentmanagement.service.chatbot=DEBUG
logging.level.ai.djl=INFO

# Email configuration
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=iscjqfojaytejhmr
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
