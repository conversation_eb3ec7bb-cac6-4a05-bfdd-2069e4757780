/* You can add global styles to this file, and also import other style files */
@use '@angular/material' as mat;

html, body { height: 100%; }
body { margin: 0; font-family: <PERSON><PERSON>, "Helvetica Neue", sans-serif; }

/* Custom theme colors */
:root {
  --primary-color: #3f51b5;
  --accent-color: #ff4081;
  --warn-color: #f44336;
  --success-color: #4caf50;
  --background-color: #fafafa;
  --surface-color: #ffffff;
  --text-primary: #212121;
  --text-secondary: #757575;
}

/* Global utility classes */
.full-width {
  width: 100%;
}

.text-center {
  text-align: center;
}

.mt-20 {
  margin-top: 20px;
}

.mb-20 {
  margin-bottom: 20px;
}

.p-20 {
  padding: 20px;
}

/* Card styles */
.mat-card {
  border-radius: 12px !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  transition: box-shadow 0.3s ease;
}

.mat-card:hover {
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15) !important;
}

/* Button styles */
.mat-raised-button {
  border-radius: 8px !important;
  font-weight: 500 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

/* Form field styles */
.mat-form-field {
  width: 100%;
}

/* Table styles */
.mat-table {
  border-radius: 8px;
  overflow: hidden;
}

.mat-header-row {
  background-color: var(--primary-color);
  color: white;
}

.mat-header-cell {
  color: white !important;
  font-weight: 500 !important;
}

/* Navigation styles */
.mat-toolbar {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Dashboard cards */
.dashboard-card {
  background: linear-gradient(135deg, var(--primary-color), #5c6bc0);
  color: white;
  border-radius: 12px;
  padding: 24px;
  margin: 16px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.dashboard-card h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 500;
}

.dashboard-card p {
  margin: 0;
  font-size: 36px;
  font-weight: 300;
}

/* Responsive design */
@media (max-width: 768px) {
  .mat-card {
    margin: 8px;
  }
  
  .dashboard-card {
    margin: 8px;
    padding: 16px;
  }
  
  .dashboard-card h2 {
    font-size: 20px;
  }
  
  .dashboard-card p {
    font-size: 28px;
  }
} 