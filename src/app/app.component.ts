import { Component, OnInit } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { filter } from 'rxjs/operators';
import { AuthService } from './services/auth.service';
import { User } from './models';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit {
  title = 'Student Management System';
  currentUser: User | null = null;
  currentRoute: string = '';

  constructor(
    private authService: AuthService,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
    });

    // Track current route
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe((event: any) => {
      this.currentRoute = event.url;
    });
  }

  isAuthenticated(): boolean {
    return this.authService.isAuthenticated();
  }

  shouldShowNavigation(): boolean {
    console.log('Current route:', this.currentRoute);
    console.log('Is authenticated:', this.authService.isAuthenticated());
    
    // Don't show navigation on login or register pages
    if (this.currentRoute === '/login' || this.currentRoute === '/register') {
      console.log('On login/register page - hiding navigation');
      return false;
    }
    // Only show navigation if user is authenticated
    const shouldShow = this.authService.isAuthenticated();
    console.log('Should show navigation:', shouldShow);
    return shouldShow;
  }

  isAdmin(): boolean {
    return this.authService.isAdmin();
  }

  logout(): void {
    this.authService.logout();
  }
} 