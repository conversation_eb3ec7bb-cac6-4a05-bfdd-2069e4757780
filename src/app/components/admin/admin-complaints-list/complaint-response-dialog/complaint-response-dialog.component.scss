.complaint-response-dialog {
  min-width: 600px;
  max-width: 700px;

  h2 {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
    color: #1976d2;

    mat-icon {
      font-size: 1.5rem;
      width: 1.5rem;
      height: 1.5rem;
    }
  }
}

mat-dialog-content {
  padding: 20px 24px;
  max-height: 80vh;
  overflow-y: auto;
}

.response-content {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.complaint-summary {
  h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 1.2rem;
    font-weight: 500;
  }

  .summary-card {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #1976d2;

    .summary-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 15px;

      .complaint-title {
        font-weight: 600;
        font-size: 1.1rem;
        color: #333;
        flex: 1;
        margin-right: 15px;
      }

      mat-chip {
        font-size: 0.8rem;
      }
    }

    .summary-meta {
      display: flex;
      gap: 20px;
      margin-bottom: 15px;

      .meta-item {
        display: flex;
        align-items: center;
        gap: 6px;
        color: #666;
        font-size: 0.9rem;

        mat-icon {
          font-size: 1rem;
          width: 1rem;
          height: 1rem;
        }
      }
    }

    .summary-description {
      color: #555;
      line-height: 1.5;
      font-size: 0.95rem;
    }
  }
}

.response-form {
  display: flex;
  flex-direction: column;
  gap: 25px;

  .full-width {
    width: 100%;
  }

  h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 1.1rem;
    font-weight: 500;
  }
}

.templates-section {
  .templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 10px;

    .template-button {
      text-align: left;
      padding: 15px;
      height: auto;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      transition: all 0.2s ease;

      &:hover {
        border-color: #1976d2;
        background-color: #f3f8ff;
      }

      .template-title {
        font-weight: 500;
        color: #333;
        margin-bottom: 8px;
        font-size: 0.9rem;
      }

      .template-preview {
        color: #666;
        font-size: 0.8rem;
        line-height: 1.3;
      }
    }
  }
}

.response-input-section {
  textarea {
    resize: vertical;
    min-height: 120px;
  }
}

.status-section {
  .status-option {
    display: flex;
    align-items: center;
    gap: 8px;

    mat-icon {
      font-size: 1.1rem;
      width: 1.1rem;
      height: 1.1rem;
    }
  }
}

.current-response-section {
  .current-response {
    background: #fff3e0;
    padding: 15px;
    border-radius: 6px;
    border-left: 4px solid #ff9800;
    color: #333;
    line-height: 1.5;
    white-space: pre-wrap;
    margin-bottom: 10px;
  }

  .response-meta {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #666;
    font-size: 0.85rem;

    mat-icon {
      font-size: 1rem;
      width: 1rem;
      height: 1rem;
    }
  }
}

.guidelines-section {
  .guidelines {
    background: #e8f5e8;
    padding: 15px;
    border-radius: 6px;
    border-left: 4px solid #4caf50;

    .guideline-item {
      display: flex;
      align-items: flex-start;
      gap: 10px;
      margin-bottom: 10px;
      color: #333;
      font-size: 0.9rem;

      &:last-child {
        margin-bottom: 0;
      }

      mat-icon {
        color: #4caf50;
        font-size: 1.1rem;
        width: 1.1rem;
        height: 1.1rem;
        margin-top: 2px;
        flex-shrink: 0;
      }

      span {
        line-height: 1.4;
      }
    }
  }
}

mat-dialog-actions {
  padding: 16px 24px;
  gap: 10px;

  button {
    display: flex;
    align-items: center;
    gap: 8px;

    .button-spinner {
      margin-right: 8px;
    }

    mat-icon {
      font-size: 1.1rem;
      width: 1.1rem;
      height: 1.1rem;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .complaint-response-dialog {
    min-width: 90vw;
    max-width: 90vw;
  }

  mat-dialog-content {
    padding: 15px;
  }

  .summary-header {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 10px;

    .complaint-title {
      margin-right: 0 !important;
    }
  }

  .summary-meta {
    flex-direction: column;
    gap: 10px !important;
  }

  .templates-grid {
    grid-template-columns: 1fr;
  }

  mat-dialog-actions {
    padding: 15px;
    flex-direction: column;

    button {
      width: 100%;
      justify-content: center;
    }
  }
}
