.complaints-list-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
  text-align: center;

  h1 {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin: 0 0 10px 0;
    color: #1976d2;
    font-size: 2.5rem;
    font-weight: 300;

    mat-icon {
      font-size: 2.5rem;
      width: 2.5rem;
      height: 2.5rem;
    }
  }

  p {
    color: #666;
    font-size: 1.1rem;
    margin: 0;
  }
}

.filters-card {
  margin-bottom: 20px;

  .filters-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 20px;
    margin-bottom: 15px;

    .filter-group {
      display: flex;
      gap: 15px;
      flex: 1;

      mat-form-field {
        min-width: 200px;
      }
    }

    .action-group {
      display: flex;
      gap: 10px;
      align-items: center;

      button {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
  }

  .results-info {
    color: #666;
    font-size: 0.9rem;
    padding-top: 10px;
    border-top: 1px solid #e0e0e0;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  gap: 20px;

  p {
    color: #666;
    font-size: 1.1rem;
  }
}

.table-card {
  .table-container {
    overflow-x: auto;
    min-height: 400px;

    .complaints-table {
      width: 100%;
      
      .mat-mdc-header-cell {
        font-weight: 600;
        color: #333;
      }

      .mat-mdc-row {
        transition: background-color 0.2s ease;

        &:hover {
          background-color: #f5f5f5;
        }

        &.urgent-row {
          background-color: #fff3e0;
          border-left: 3px solid #ff9800;

          &:hover {
            background-color: #ffe0b2;
          }
        }
      }

      .title-cell {
        .complaint-title {
          display: block;
          font-weight: 500;
          font-size: 1rem;
          margin-bottom: 4px;
          color: #333;
        }

        .complaint-preview {
          display: block;
          font-size: 0.85rem;
          color: #666;
          line-height: 1.3;
        }
      }

      .student-cell {
        display: flex;
        align-items: center;
        gap: 8px;

        mat-icon {
          color: #1976d2;
          font-size: 1.1rem;
          width: 1.1rem;
          height: 1.1rem;
        }
      }

      .date-cell {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #666;

        mat-icon {
          font-size: 1rem;
          width: 1rem;
          height: 1rem;
        }
      }

      .subject-cell {
        display: flex;
        align-items: center;
        gap: 8px;

        mat-icon {
          color: #4caf50;
          font-size: 1.1rem;
          width: 1.1rem;
          height: 1.1rem;
        }
      }

      .no-subject {
        color: #999;
        font-style: italic;
        font-size: 0.9rem;
      }

      mat-chip {
        display: flex;
        align-items: center;
        gap: 5px;

        mat-icon {
          font-size: 0.9rem;
          width: 0.9rem;
          height: 0.9rem;
        }
      }

      .action-buttons {
        display: flex;
        gap: 5px;

        button {
          &:disabled {
            opacity: 0.5;
          }

          mat-icon {
            font-size: 1.1rem;
            width: 1.1rem;
            height: 1.1rem;
          }
        }
      }
    }
  }

  .no-data {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: #666;
    text-align: center;

    mat-icon {
      font-size: 4rem;
      width: 4rem;
      height: 4rem;
      margin-bottom: 20px;
      opacity: 0.5;
    }

    h3 {
      margin: 0 0 10px 0;
      font-size: 1.5rem;
      font-weight: 400;
    }

    p {
      margin: 0;
      font-size: 1rem;
      line-height: 1.5;
    }
  }
}

// Responsive design
@media (max-width: 1024px) {
  .complaints-list-container {
    padding: 15px;
  }

  .filters-row {
    flex-direction: column;
    align-items: stretch !important;

    .filter-group {
      flex-direction: column;

      mat-form-field {
        min-width: auto;
      }
    }

    .action-group {
      justify-content: center;
      margin-top: 15px;
    }
  }
}

@media (max-width: 768px) {
  .page-header h1 {
    font-size: 2rem;

    mat-icon {
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
    }
  }

  .complaints-table {
    .title-cell .complaint-preview {
      display: none;
    }

    .subject-cell,
    .date-cell {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;

      mat-icon {
        display: none;
      }
    }
  }

  .action-buttons {
    flex-direction: column;
  }
}

@media (max-width: 600px) {
  .table-container {
    .complaints-table {
      font-size: 0.85rem;

      .mat-mdc-header-cell,
      .mat-mdc-cell {
        padding: 8px 4px;
      }
    }
  }

  .filters-row .filter-group {
    gap: 10px;
  }
}
