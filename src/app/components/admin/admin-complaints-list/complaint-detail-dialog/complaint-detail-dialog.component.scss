.complaint-detail-dialog {
  min-width: 700px;
  max-width: 800px;

  h2 {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
    color: #1976d2;

    mat-icon {
      font-size: 1.5rem;
      width: 1.5rem;
      height: 1.5rem;
    }

    .urgent-chip {
      background: #f44336 !important;
      color: white;
      margin-left: auto;
      font-size: 0.75rem;

      mat-icon {
        font-size: 1rem;
        width: 1rem;
        height: 1rem;
      }
    }
  }
}

mat-dialog-content {
  padding: 20px 24px;
  max-height: 80vh;
  overflow-y: auto;
}

.complaint-content {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.complaint-header {
  .header-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;

    .complaint-title h3 {
      margin: 0;
      color: #333;
      font-size: 1.5rem;
      font-weight: 500;
    }

    .status-section {
      mat-chip {
        display: flex;
        align-items: center;
        gap: 5px;

        mat-icon {
          font-size: 1rem;
          width: 1rem;
          height: 1rem;
        }
      }
    }
  }

  .meta-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;

    .meta-item {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 10px;
      background: #f8f9fa;
      border-radius: 6px;

      mat-icon {
        color: #1976d2;
        font-size: 1.1rem;
        width: 1.1rem;
        height: 1.1rem;
      }

      .label {
        font-weight: 500;
        color: #666;
        min-width: 70px;
      }

      .value {
        color: #333;
        font-weight: 500;
      }
    }
  }
}

.related-info {
  h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 1.2rem;
    font-weight: 500;
  }

  .related-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;

    .related-item {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      padding: 15px;
      background: #e3f2fd;
      border-radius: 8px;
      border-left: 4px solid #1976d2;

      mat-icon {
        color: #1976d2;
        font-size: 1.3rem;
        width: 1.3rem;
        height: 1.3rem;
        margin-top: 2px;
      }

      .related-content {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .related-label {
          font-weight: 500;
          color: #1976d2;
          font-size: 0.9rem;
        }

        .related-value {
          font-weight: 600;
          color: #333;
          font-size: 1.1rem;
        }

        .related-detail {
          color: #666;
          font-size: 0.85rem;
        }
      }
    }
  }
}

.description-section {
  h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 1.2rem;
    font-weight: 500;
  }

  .description-content {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #4caf50;
    line-height: 1.6;
    color: #333;
    white-space: pre-wrap;
  }
}

.response-section {
  h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 1.2rem;
    font-weight: 500;
  }

  .response-content {
    background: #fff3e0;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #ff9800;
    line-height: 1.6;
    color: #333;
    white-space: pre-wrap;
  }

  .response-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 10px;
    color: #666;
    font-size: 0.9rem;

    mat-icon {
      font-size: 1rem;
      width: 1rem;
      height: 1rem;
    }
  }
}

.status-update-section {
  h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 1.2rem;
    font-weight: 500;
  }

  .status-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;

    .status-button {
      display: flex;
      align-items: center;
      gap: 8px;
      min-width: 120px;

      mat-icon {
        font-size: 1.1rem;
        width: 1.1rem;
        height: 1.1rem;
      }
    }
  }
}

.timeline-section {
  h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 1.2rem;
    font-weight: 500;
  }

  .timeline {
    display: flex;
    flex-direction: column;
    gap: 15px;

    .timeline-item {
      display: flex;
      align-items: flex-start;
      gap: 15px;

      .timeline-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        flex-shrink: 0;

        mat-icon {
          font-size: 1.2rem;
          width: 1.2rem;
          height: 1.2rem;
          color: white;
        }

        &.created {
          background: #4caf50;
        }

        &.updated {
          background: #2196f3;
        }
      }

      .timeline-content {
        .timeline-title {
          font-weight: 500;
          color: #333;
          margin-bottom: 4px;
        }

        .timeline-date {
          color: #666;
          font-size: 0.9rem;
        }
      }
    }
  }
}

mat-dialog-actions {
  padding: 16px 24px;
  gap: 10px;

  button {
    display: flex;
    align-items: center;
    gap: 8px;

    .button-spinner {
      margin-right: 8px;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .complaint-detail-dialog {
    min-width: 90vw;
    max-width: 90vw;
  }

  mat-dialog-content {
    padding: 15px;
  }

  .complaint-header .header-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .meta-info {
    grid-template-columns: 1fr;
  }

  .related-grid {
    grid-template-columns: 1fr;
  }

  .status-buttons {
    flex-direction: column;

    .status-button {
      width: 100%;
      justify-content: center;
    }
  }

  mat-dialog-actions {
    padding: 15px;
    flex-direction: column;

    button {
      width: 100%;
      justify-content: center;
    }
  }
}

@media print {
  .mat-dialog-actions {
    display: none;
  }

  .urgent-chip {
    print-color-adjust: exact;
  }
}

