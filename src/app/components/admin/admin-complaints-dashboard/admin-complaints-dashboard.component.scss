.complaints-dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.dashboard-header {
  text-align: center;
  margin-bottom: 40px;
  position: relative;

  h1 {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin: 0 0 8px 0;
    color: #1976d2;
    font-size: 2.5rem;
    font-weight: 300;

    mat-icon {
      font-size: 2.5rem;
      width: 2.5rem;
      height: 2.5rem;
    }
  }

  p {
    margin: 0 0 20px 0;
    color: #666;
    font-size: 1.1rem;
  }

  .header-actions {
    display: flex;
    justify-content: center;
    gap: 15px;

    button {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;

  p {
    margin-top: 16px;
    color: #666;
  }
}

.dashboard-content {
  animation: fadeIn 0.5s ease-in;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.dashboard-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border-left: 4px solid transparent;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  .card-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    border-radius: 12px;
    
    mat-icon {
      font-size: 24px;
      width: 24px;
      height: 24px;
      color: white;
    }
  }

  .card-content {
    flex: 1;

    h2 {
      margin: 0 0 4px 0;
      font-size: 2rem;
      font-weight: 600;
    }

    p {
      margin: 0;
      color: #666;
      font-size: 0.9rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .progress-bar {
      width: 100%;
      height: 4px;
      background: #e0e0e0;
      border-radius: 2px;
      margin-top: 8px;
      overflow: hidden;

      .progress-fill {
        height: 100%;
        border-radius: 2px;
        transition: width 0.3s ease;

        &.success {
          background: linear-gradient(90deg, #4caf50, #66bb6a);
        }
      }
    }
  }

  &.total {
    border-left-color: #1976d2;
    .card-icon { background: linear-gradient(135deg, #1976d2, #42a5f5); }
    .card-content h2 { color: #1976d2; }
    .progress-fill { background: linear-gradient(90deg, #1976d2, #42a5f5); }
  }

  &.pending {
    border-left-color: #ff9800;
    .card-icon { background: linear-gradient(135deg, #ff9800, #ffb74d); }
    .card-content h2 { color: #ff9800; }
    .progress-fill { background: linear-gradient(90deg, #ff9800, #ffb74d); }
  }

  &.reviewed {
    border-left-color: #2196f3;
    .card-icon { background: linear-gradient(135deg, #2196f3, #64b5f6); }
    .card-content h2 { color: #2196f3; }
  }

  &.resolved {
    border-left-color: #4caf50;
    .card-icon { background: linear-gradient(135deg, #4caf50, #66bb6a); }
    .card-content h2 { color: #4caf50; }
  }

  &.rejected {
    border-left-color: #f44336;
    .card-icon { background: linear-gradient(135deg, #f44336, #ef5350); }
    .card-content h2 { color: #f44336; }
  }

  &.resolution-rate {
    border-left-color: #9c27b0;
    .card-icon { background: linear-gradient(135deg, #9c27b0, #ba68c8); }
    .card-content h2 { color: #9c27b0; }
  }
}

.time-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;

  .time-stat-card {
    .time-stat {
      display: flex;
      align-items: center;
      gap: 16px;

      mat-icon {
        font-size: 2rem;
        width: 2rem;
        height: 2rem;
        color: #1976d2;
      }

      .time-stat-info {
        h3 {
          margin: 0 0 4px 0;
          font-size: 1.8rem;
          font-weight: 600;
          color: #333;
        }

        p {
          margin: 0;
          color: #666;
          font-size: 0.9rem;
        }
      }
    }
  }
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.content-card {
  height: fit-content;

  &.urgent {
    border-left: 4px solid #f44336;
  }

  mat-card-header {
    padding-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 18px;
      font-weight: 500;

      mat-icon {
        color: #1976d2;
      }

      .urgent-badge {
        background: #f44336;
        color: white;
        font-size: 0.8rem;
        height: 20px;
        min-height: 20px;
      }
    }

    .card-actions button {
      color: #1976d2;
    }
  }
}

.complaint-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.complaint-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #e9ecef;
    transform: translateX(4px);
  }

  &.urgent-item {
    background: #fff3e0;
    border-left: 3px solid #ff9800;

    &:hover {
      background: #ffe0b2;
    }
  }

  .complaint-status {
    margin-right: 12px;

    mat-icon {
      font-size: 20px;
      width: 20px;
      height: 20px;

      &.urgent-icon {
        color: #f44336;
      }
    }
  }

  .complaint-info {
    flex: 1;

    .complaint-title {
      font-weight: 500;
      color: #333;
      margin-bottom: 4px;
      font-size: 1rem;
    }

    .complaint-meta {
      display: flex;
      gap: 12px;
      font-size: 0.85rem;
      color: #666;

      .student-name {
        font-weight: 500;
      }
    }
  }

  .complaint-status-badge {
    mat-chip {
      font-size: 0.75rem;
      height: 24px;
      min-height: 24px;
    }
  }

  .days-pending {
    font-size: 0.8rem;
    color: #f44336;
    font-weight: 500;
    background: #ffebee;
    padding: 4px 8px;
    border-radius: 4px;
  }
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  color: #666;

  mat-icon {
    font-size: 3rem;
    width: 3rem;
    height: 3rem;
    margin-bottom: 10px;
    opacity: 0.5;
  }

  p {
    margin: 0;
    font-style: italic;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive design
@media (max-width: 768px) {
  .complaints-dashboard-container {
    padding: 15px;
  }

  .dashboard-header h1 {
    font-size: 2rem;

    mat-icon {
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
    }
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
  }

  .content-grid {
    grid-template-columns: 1fr;
  }

  .dashboard-card {
    padding: 16px;
    flex-direction: column;
    text-align: center;

    .card-content h2 {
      font-size: 1.5rem;
    }
  }
}
