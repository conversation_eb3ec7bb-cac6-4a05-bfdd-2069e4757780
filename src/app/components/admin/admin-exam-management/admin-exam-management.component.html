<div class="admin-exam-management">
  <div class="header">
    <h1>Exam Management</h1>
    <button mat-raised-button color="primary" (click)="openExamFormDialog()">
      <mat-icon>add</mat-icon>
      Add New Exam
    </button>
  </div>

  <div class="content">
    <mat-card>
      <mat-card-content>
        <div *ngIf="loading" class="loading">
          <mat-spinner></mat-spinner>
          <p>Loading exams...</p>
        </div>

        <div *ngIf="!loading">
          <table mat-table [dataSource]="exams" class="exam-table">
            <!-- Title Column -->
            <ng-container matColumnDef="title">
              <th mat-header-cell *matHeaderCellDef>Title</th>
              <td mat-cell *matCellDef="let exam">{{ exam.title }}</td>
            </ng-container>

            <!-- Subject Column -->
            <ng-container matColumnDef="subject">
              <th mat-header-cell *matHeaderCellDef>Subject</th>
              <td mat-cell *matCellDef="let exam">{{ exam.subjectName }}</td>
            </ng-container>

            <!-- Date Column -->
            <ng-container matColumnDef="date">
              <th mat-header-cell *matHeaderCellDef>Date</th>
              <td mat-cell *matCellDef="let exam">{{ formatDate(exam.date) }}</td>
            </ng-container>

            <!-- Start Time Column -->
            <ng-container matColumnDef="startTime">
              <th mat-header-cell *matHeaderCellDef>Start Time</th>
              <td mat-cell *matCellDef="let exam">{{ formatTime(exam.startTime) }}</td>
            </ng-container>

            <!-- End Time Column -->
            <ng-container matColumnDef="endTime">
              <th mat-header-cell *matHeaderCellDef>End Time</th>
              <td mat-cell *matCellDef="let exam">{{ formatTime(exam.endTime) }}</td>
            </ng-container>

            <!-- Location Column -->
            <ng-container matColumnDef="location">
              <th mat-header-cell *matHeaderCellDef>Location</th>
              <td mat-cell *matCellDef="let exam">{{ exam.location }}</td>
            </ng-container>

            <!-- Actions Column -->
            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef>Actions</th>
              <td mat-cell *matCellDef="let exam">
                <button mat-icon-button color="primary" (click)="openExamFormDialog(exam)" matTooltip="Edit">
                  <mat-icon>edit</mat-icon>
                </button>
                <button mat-icon-button color="warn" (click)="deleteExam(exam)" matTooltip="Delete">
                  <mat-icon>delete</mat-icon>
                </button>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
          </table>

          <div *ngIf="exams.length === 0" class="no-data">
            <mat-icon>event_busy</mat-icon>
            <p>No exams found. Create your first exam!</p>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div> 