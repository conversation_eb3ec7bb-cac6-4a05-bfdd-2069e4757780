<div class="profile-container">
  <div class="profile-header">
    <h1>Profile</h1>
    <p>Your account information</p>
  </div>

  <div class="profile-content" *ngIf="currentUser">
    <mat-card class="profile-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>account_circle</mat-icon>
          Account Information
        </mat-card-title>
      </mat-card-header>
      
      <mat-card-content>
        <div class="profile-info">
          <div class="info-row">
            <span class="info-label">Username:</span>
            <span class="info-value">{{ currentUser.username }}</span>
          </div>
          
          <div class="info-row">
            <span class="info-label">Email:</span>
            <span class="info-value">{{ currentUser.email }}</span>
          </div>
          
          <div class="info-row">
            <span class="info-label">Role:</span>
            <span class="info-value">
              <mat-chip color="primary" selected>
                {{ getRoleDisplayName(currentUser.role) }}
              </mat-chip>
            </span>
          </div>
          
          <div class="info-row">
            <span class="info-label">User ID:</span>
            <span class="info-value">{{ currentUser.id }}</span>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <mat-card class="profile-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>security</mat-icon>
          Security Information
        </mat-card-title>
      </mat-card-header>
      
      <mat-card-content>
        <p>Your account is secured with JWT authentication.</p>
        <p>Last login: <span class="text-muted">Current session</span></p>
      </mat-card-content>
    </mat-card>
  </div>

  <div class="loading-container" *ngIf="!currentUser">
    <mat-spinner diameter="50"></mat-spinner>
    <p>Loading profile...</p>
  </div>
</div> 