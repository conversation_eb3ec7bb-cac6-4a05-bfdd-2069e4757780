<div class="dashboard-container">
  <div class="dashboard-header">
    <h1>Dashboard</h1>
    <p>Welcome to the Student Management System</p>
  </div>

  <div class="loading-container" *ngIf="loading">
    <mat-spinner diameter="50"></mat-spinner>
    <p>Loading dashboard data...</p>
  </div>

  <div class="dashboard-content" *ngIf="!loading">
    <!-- Stats Cards -->
    <div class="stats-grid">
      <div class="dashboard-card">
        <mat-icon>people</mat-icon>
        <h2>Total Students</h2>
        <p>{{ totalStudents }}</p>
      </div>

      <div class="dashboard-card">
        <mat-icon>book</mat-icon>
        <h2>Total Subjects</h2>
        <p>{{ totalSubjects }}</p>
      </div>

      <div class="dashboard-card">
        <mat-icon>grade</mat-icon>
        <h2>Total Grades</h2>
        <p>{{ totalGrades }}</p>
      </div>

      <div class="dashboard-card">
        <mat-icon>trending_up</mat-icon>
        <h2>Average Grade</h2>
        <p>{{ averageGrade | number:'1.1-1' }}</p>
      </div>
    </div>

    <!-- Content Grid -->
    <div class="content-grid">
      <!-- Top Students -->
      <mat-card class="content-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>star</mat-icon>
            Top Students
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="student-list" *ngIf="getTopStudents().length > 0">
            <div class="student-item" *ngFor="let student of getTopStudents(); let i = index">
              <div class="student-rank">{{ i + 1 }}</div>
              <div class="student-info">
                <div class="student-name">{{ student.prenom }} {{ student.nom }}</div>
                <div class="student-average">Average: {{ student.moyenne | number:'1.1-1' }}</div>
              </div>
            </div>
          </div>
          <p *ngIf="getTopStudents().length === 0" class="no-data">No students found</p>
        </mat-card-content>
      </mat-card>

      <!-- Recent Grades -->
      <mat-card class="content-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>schedule</mat-icon>
            Recent Grades
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="grade-list" *ngIf="getRecentGrades().length > 0">
            <div class="grade-item" *ngFor="let grade of getRecentGrades()">
              <div class="grade-info">
                <div class="grade-student">{{ grade.etudiant?.prenom }} {{ grade.etudiant?.nom }}</div>
                <div class="grade-subject">{{ grade.matiere?.nom }}</div>
              </div>
              <div class="grade-value">{{ grade.valeur | number:'1.1-1' }}</div>
            </div>
          </div>
          <p *ngIf="getRecentGrades().length === 0" class="no-data">No grades found</p>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</div> 