<div class="grade-list-container">
  <div class="grade-list-header">
    <h1>Grades</h1>
    <p>Manage student grades and assessments</p>
  </div>

  <mat-card>
    <mat-card-header>
      <mat-card-title>
        <mat-icon>grade</mat-icon>
        Grade List
      </mat-card-title>
    </mat-card-header>

    <mat-card-content>
      <div class="table-controls">
        <mat-form-field appearance="outline">
          <mat-label>Filter</mat-label>
          <input matInput (keyup)="applyFilter($event)" placeholder="Search grades...">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>

        <button mat-raised-button color="primary" (click)="addGrade()">
          <mat-icon>add</mat-icon>
          Add Grade
        </button>
      </div>

      <div class="loading-container" *ngIf="loading">
        <mat-spinner diameter="50"></mat-spinner>
        <p>Loading grades...</p>
      </div>

      <div class="table-container" *ngIf="!loading">
        <table mat-table [dataSource]="dataSource" matSort>

          <ng-container matColumnDef="id">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>ID</th>
            <td mat-cell *matCellDef="let grade">{{ grade.id }}</td>
          </ng-container>

          <ng-container matColumnDef="valeur">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Grade</th>
            <td mat-cell *matCellDef="let grade">
              <span class="grade-value" [ngClass]="getGradeClass(grade.valeur)">
                {{ grade.valeur }}/20
              </span>
            </td>
          </ng-container>

          <ng-container matColumnDef="etudiant">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Student</th>
            <td mat-cell *matCellDef="let grade">
              <div class="student-info">
                <mat-icon class="student-icon">person</mat-icon>
                {{ getStudentName(grade) }}
              </div>
            </td>
          </ng-container>

          <ng-container matColumnDef="matiere">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Subject</th>
            <td mat-cell *matCellDef="let grade">
              <div class="subject-info">
                <mat-icon class="subject-icon">book</mat-icon>
                {{ getSubjectName(grade) }}
              </div>
            </td>
          </ng-container>

            <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let grade">
                    <div class="action-buttons">
                        <button mat-icon-button color="accent" (click)="editGrade(grade)" matTooltip="Edit">
                            <mat-icon>edit</mat-icon>
                        </button>
                        <button mat-icon-button color="warn" (click)="deleteGrade(grade)" matTooltip="Delete">
                            <mat-icon>delete</mat-icon>
                        </button>
                    </div>
                </td>
            </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>

        <mat-paginator [pageSizeOptions]="[5, 10, 25, 100]" aria-label="Select page of grades"></mat-paginator>
      </div>
    </mat-card-content>
  </mat-card>
</div>
