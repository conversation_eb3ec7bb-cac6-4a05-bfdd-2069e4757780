.dialog-container {
  min-width: 400px;
  max-width: 500px;
}

.subject-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px 0;
}

.full-width {
  width: 100%;
}

h2[mat-dialog-title] {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  padding: 0;
  
  mat-icon {
    color: #1976d2;
  }
}

mat-dialog-actions {
  padding: 16px 0 0 0;
  margin: 0;
  
  button {
    margin-left: 8px;
    
    &:first-child {
      margin-left: 0;
    }
  }
}

mat-spinner {
  margin-right: 8px;
}

.coefficient-preview {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background-color: #f5f5f5;
  border-radius: 8px;
  margin-top: 8px;
  
  mat-icon {
    color: var(--primary-color);
  }
  
  .preview-text {
    color: var(--text-secondary);
    font-size: 14px;
  }
  
  .coefficient-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 24px;
    padding: 0 8px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 12px;
    
    &.high-coefficient {
      background-color: #ffebee;
      color: #c62828;
      border: 1px solid #ffcdd2;
    }
    
    &.medium-coefficient {
      background-color: #fff3e0;
      color: #ef6c00;
      border: 1px solid #ffcc02;
    }
    
    &.low-coefficient {
      background-color: #e8f5e8;
      color: #2e7d32;
      border: 1px solid #c8e6c9;
    }
  }
}

@media (max-width: 480px) {
  .dialog-container {
    min-width: 300px;
    max-width: 350px;
  }
  
  .coefficient-preview {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
