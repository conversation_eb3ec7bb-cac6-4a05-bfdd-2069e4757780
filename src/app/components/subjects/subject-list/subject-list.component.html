<div class="subject-list-container">
  <div class="subject-list-header">
    <h1>Subjects</h1>
    <p>Manage academic subjects and their coefficients</p>
  </div>

  <mat-card>
    <mat-card-header>
      <mat-card-title>
        <mat-icon>book</mat-icon>
        Subject List
      </mat-card-title>
    </mat-card-header>
    
    <mat-card-content>
      <div class="table-controls">
        <mat-form-field appearance="outline">
          <mat-label>Filter</mat-label>
          <input matInput (keyup)="applyFilter($event)" placeholder="Search subjects...">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>
        
        <button mat-raised-button color="primary" (click)="addSubject()">
          <mat-icon>add</mat-icon>
          Add Subject
        </button>
      </div>

      <div class="loading-container" *ngIf="loading">
        <mat-spinner diameter="50"></mat-spinner>
        <p>Loading subjects...</p>
      </div>

      <div class="table-container" *ngIf="!loading">
        <table mat-table [dataSource]="dataSource" matSort>
          <ng-container matColumnDef="id">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>ID</th>
            <td mat-cell *matCellDef="let subject">{{ subject.id }}</td>
          </ng-container>

          <ng-container matColumnDef="nom">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Subject Name</th>
            <td mat-cell *matCellDef="let subject">
              <div class="subject-name">
                <mat-icon class="subject-icon">book</mat-icon>
                {{ subject.nom }}
              </div>
            </td>
          </ng-container>

          <ng-container matColumnDef="coefficient">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Coefficient</th>
            <td mat-cell *matCellDef="let subject">
              <span class="coefficient-badge" [ngClass]="getCoefficientClass(subject.coefficient)">
                {{ subject.coefficient }}
              </span>
            </td>
          </ng-container>

            <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let subject">
                    <div class="action-buttons">
                        <button mat-icon-button color="accent" (click)="editSubject(subject)" matTooltip="Edit">
                            <mat-icon>edit</mat-icon>
                        </button>
                        <button mat-icon-button color="warn" (click)="deleteSubject(subject)" matTooltip="Delete">
                            <mat-icon>delete</mat-icon>
                        </button>
                    </div>
                </td>
            </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>

        <mat-paginator [pageSizeOptions]="[5, 10, 25, 100]" aria-label="Select page of subjects"></mat-paginator>
      </div>
    </mat-card-content>
  </mat-card>
</div>
