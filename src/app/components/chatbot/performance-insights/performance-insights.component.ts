import { Component, OnDestroy, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { AuthService } from '../../../services/auth.service';

interface ChatMessage {
  text: string;
  sender: 'user' | 'bot';
  timestamp: Date;
}

@Component({
  selector: 'app-performance-insights',
  templateUrl: './performance-insights.component.html',
  styleUrls: ['./performance-insights.component.scss']
})
export class PerformanceInsightsComponent implements OnInit, OnDestroy {
  question: string = '';
  messages: ChatMessage[] = [];
  isLoading: boolean = false;
  private apiUrl = 'http://localhost:8080/api/chatbot/ask';

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    // Add welcome message
    this.messages.push({
      text: 'Hello! I\'m your Performance Insights Bot. Ask me about your academic performance, subjects, or grades!',
      sender: 'bot',
      timestamp: new Date()
    });
  }

  ngOnDestroy(): void {
    // Cleanup if needed
  }

  askQuestion(): void {
    if (!this.question.trim() || this.isLoading) return;

    // Add user message to chat
    this.messages.push({
      text: this.question,
      sender: 'user',
      timestamp: new Date()
    });

    this.isLoading = true;
    const userQuestion = this.question;
    this.question = '';

    // Send request to backend with authentication headers
    const token = this.authService.getToken();
    const headers = token ? { 'Authorization': `Bearer ${token}` } : {};

    this.http.post<any>(this.apiUrl, {
      question: userQuestion
    }, { headers }).subscribe({
      next: (response) => {
        this.isLoading = false;
        if (response.error) {
          this.messages.push({
            text: 'Sorry, I encountered an error: ' + response.error,
            sender: 'bot',
            timestamp: new Date()
          });
        } else {
          this.messages.push({
            text: response.answer,
            sender: 'bot',
            timestamp: new Date()
          });
        }
      },
      error: (error) => {
        this.isLoading = false;
        this.messages.push({
          text: 'Sorry, I encountered an error. Please try again.',
          sender: 'bot',
          timestamp: new Date()
        });
      }
    });
  }

  onKeyPress(event: Event): void {
    const keyboardEvent = event as KeyboardEvent;
    if (keyboardEvent.key === 'Enter' && !keyboardEvent.shiftKey) {
      keyboardEvent.preventDefault();
      this.askQuestion();
    }
  }
}
