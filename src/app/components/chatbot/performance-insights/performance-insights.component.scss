.chatbot-container {
  display: flex;
  flex-direction: column;
  height: 600px;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin: 20px;
}

.chat-header {
  padding: 20px;
  background-color: #1976d2;
  color: white;
  
  h2 {
    margin: 0 0 8px 0;
    font-size: 1.5rem;
  }
  
  p {
    margin: 0;
    opacity: 0.9;
    font-size: 0.9rem;
  }
}

.chat-messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.message {
  margin-bottom: 15px;
  max-width: 80%;
  align-self: flex-start;
  
  &.user-message {
    align-self: flex-end;
    
    .message-content {
      background-color: #1976d2;
      color: white;
      border-radius: 18px 18px 0 18px;
    }
  }
  
  &.bot-message {
    .message-content {
      background-color: #e0e0e0;
      color: #333;
      border-radius: 18px 18px 18px 0;
    }
  }
}

.message-content {
  padding: 12px 16px;
  display: inline-block;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.message-time {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  text-align: right;
}

.chat-input {
  display: flex;
  padding: 15px;
  background-color: white;
  border-top: 1px solid #ddd;
  
  textarea {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 24px;
    outline: none;
    resize: none;
    font-family: inherit;
    
    &:focus {
      border-color: #1976d2;
      box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
    }
    
    &:disabled {
      background-color: #f5f5f5;
    }
  }
  
  .send-button {
    margin-left: 10px;
    padding: 12px 24px;
    background-color: #1976d2;
    color: white;
    border: none;
    border-radius: 24px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
    
    &:hover:not(:disabled) {
      background-color: #1565c0;
    }
    
    &:disabled {
      background-color: #bdbdbd;
      cursor: not-allowed;
    }
  }
}

.typing-indicator {
  display: flex;
  align-items: center;
  padding: 5px 0;
  
  span {
    height: 8px;
    width: 8px;
    background-color: #999;
    border-radius: 50%;
    display: inline-block;
    margin: 0 2px;
    animation: typing 1s infinite;
    
    &:nth-child(2) {
      animation-delay: 0.2s;
    }
    
    &:nth-child(3) {
      animation-delay: 0.4s;
    }
  }
}

@keyframes typing {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

/* Scrollbar styling */
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
