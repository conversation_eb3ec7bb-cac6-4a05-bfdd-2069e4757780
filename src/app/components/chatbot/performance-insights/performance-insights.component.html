<div class="chatbot-container">
  <div class="chat-header">
    <h2>Performance Insights Bot</h2>
    <p>Ask me about your academic performance, subjects, or grades!</p>
  </div>
  
  <div class="chat-messages" #chatMessages>
    <div class="messages-container">
      <div *ngFor="let message of messages" class="message" [ngClass]="'message-' + message.sender">
        <div class="message-content">
          <div class="message-text">{{ message.text }}</div>
          <div class="message-time">{{ message.timestamp | date:'short' }}</div>
        </div>
      </div>
      
      <div *ngIf="isLoading" class="message message-bot">
        <div class="message-content">
          <div class="typing-indicator">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <div class="chat-input">
    <textarea 
      [(ngModel)]="question" 
      (keydown.enter)="onKeyPress($event)"
      placeholder="Ask about your performance, subjects, or grades..."
      [disabled]="isLoading"
      rows="3">
    </textarea>
    <button (click)="askQuestion()" [disabled]="isLoading || !question.trim()" class="send-button">
      Send
    </button>
  </div>
</div>
