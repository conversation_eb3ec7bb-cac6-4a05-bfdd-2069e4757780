.complaint-dialog {
  min-width: 500px;
  max-width: 600px;

  h2 {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
    color: #1976d2;

    mat-icon {
      font-size: 1.5rem;
      width: 1.5rem;
      height: 1.5rem;
    }
  }
}

mat-dialog-content {
  padding: 20px 24px;
  max-height: 70vh;
  overflow-y: auto;
}

.complaint-form {
  display: flex;
  flex-direction: column;
  gap: 20px;

  .full-width {
    width: 100%;
  }

  mat-form-field {
    .mat-mdc-form-field-subscript-wrapper {
      margin-top: 8px;
    }
  }

  textarea {
    resize: vertical;
    min-height: 100px;
  }
}

.info-card {
  background: #f8f9fa;
  border-left: 4px solid #1976d2;
  margin-top: 10px;

  mat-card-content {
    padding: 16px;

    .info-header {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 15px;

      mat-icon {
        color: #1976d2;
        font-size: 1.3rem;
        width: 1.3rem;
        height: 1.3rem;
      }

      h4 {
        margin: 0;
        color: #333;
        font-weight: 500;
      }
    }

    .info-list {
      margin: 0;
      padding-left: 20px;
      color: #666;

      li {
        margin-bottom: 8px;
        line-height: 1.4;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

mat-dialog-actions {
  padding: 16px 24px;
  gap: 10px;

  button {
    display: flex;
    align-items: center;
    gap: 8px;

    .button-spinner {
      margin-right: 8px;
    }

    mat-icon {
      font-size: 1.1rem;
      width: 1.1rem;
      height: 1.1rem;
    }
  }
}

// Responsive design
@media (max-width: 600px) {
  .complaint-dialog {
    min-width: 90vw;
    max-width: 90vw;
  }

  mat-dialog-content {
    padding: 15px;
  }

  mat-dialog-actions {
    padding: 15px;
    flex-direction: column;

    button {
      width: 100%;
      justify-content: center;
    }
  }
}
