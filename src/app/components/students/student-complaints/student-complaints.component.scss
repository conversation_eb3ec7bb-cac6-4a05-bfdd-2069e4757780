.student-complaints-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
  text-align: center;

  h1 {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin: 0 0 10px 0;
    color: #1976d2;
    font-size: 2.5rem;
    font-weight: 300;

    mat-icon {
      font-size: 2.5rem;
      width: 2.5rem;
      height: 2.5rem;
    }
  }

  .page-description {
    color: #666;
    font-size: 1.1rem;
    margin: 0;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  gap: 20px;

  p {
    color: #666;
    font-size: 1.1rem;
  }
}

.content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.action-bar {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;

  button {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;

  .stat-card {
    transition: transform 0.2s ease;

    &:hover {
      transform: translateY(-2px);
    }

    .stat-content {
      display: flex;
      align-items: center;
      gap: 15px;

      mat-icon {
        font-size: 2rem;
        width: 2rem;
        height: 2rem;
      }

      .stat-info {
        .stat-value {
          font-size: 2rem;
          font-weight: 600;
          margin-bottom: 5px;
        }

        .stat-label {
          font-size: 0.9rem;
          color: #666;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
      }
    }

    &.pending {
      border-left: 4px solid #ff9800;
      
      mat-icon {
        color: #ff9800;
      }
      
      .stat-value {
        color: #ff9800;
      }
    }

    &.reviewed {
      border-left: 4px solid #2196f3;
      
      mat-icon {
        color: #2196f3;
      }
      
      .stat-value {
        color: #2196f3;
      }
    }

    &.resolved {
      border-left: 4px solid #4caf50;
      
      mat-icon {
        color: #4caf50;
      }
      
      .stat-value {
        color: #4caf50;
      }
    }

    &.total {
      border-left: 4px solid #1976d2;
      
      mat-icon {
        color: #1976d2;
      }
      
      .stat-value {
        color: #1976d2;
      }
    }
  }
}

.complaints-card {
  .no-complaints {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40px;
    color: #666;
    gap: 20px;

    mat-icon {
      font-size: 3rem;
      width: 3rem;
      height: 3rem;
      opacity: 0.5;
    }

    p {
      font-size: 1.1rem;
      margin: 0;
      text-align: center;
    }
  }

  .complaints-table-container {
    overflow-x: auto;
    margin-top: 10px;

    .complaints-table {
      width: 100%;
      
      .title-cell {
        .complaint-title {
          display: block;
          font-weight: 500;
          font-size: 1.1rem;
          margin-bottom: 5px;
          color: #333;
        }

        .complaint-description {
          display: block;
          font-size: 0.9rem;
          color: #666;
          line-height: 1.4;
        }
      }

      .date-cell {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #666;

        mat-icon {
          font-size: 1.1rem;
          width: 1.1rem;
          height: 1.1rem;
        }
      }

      mat-chip {
        display: flex;
        align-items: center;
        gap: 5px;

        mat-icon {
          font-size: 1rem;
          width: 1rem;
          height: 1rem;
        }
      }
    }
  }
}

.info-card {
  background: #f8f9fa;
  border-left: 4px solid #1976d2;

  .info-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 15px;

    .step {
      display: flex;
      gap: 15px;
      align-items: flex-start;

      .step-number {
        background: #1976d2;
        color: white;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        flex-shrink: 0;
      }

      .step-content {
        h4 {
          margin: 0 0 8px 0;
          color: #333;
          font-size: 1rem;
        }

        p {
          margin: 0;
          color: #666;
          font-size: 0.9rem;
          line-height: 1.4;
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .student-complaints-container {
    padding: 15px;
  }

  .page-header h1 {
    font-size: 2rem;

    mat-icon {
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
    }
  }

  .action-bar {
    justify-content: center;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .info-steps {
    grid-template-columns: 1fr;
  }

  .title-cell {
    .complaint-description {
      display: none;
    }
  }
}
