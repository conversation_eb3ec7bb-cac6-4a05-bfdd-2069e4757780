<div class="student-complaints-container">
  <div class="page-header">
    <h1>
      <mat-icon>feedback</mat-icon>
      Grade Complaints
    </h1>
    <p class="page-description">Submit and track complaints about your grades</p>
  </div>

  <div *ngIf="loading" class="loading-container">
    <mat-spinner></mat-spinner>
    <p>Loading complaints...</p>
  </div>

  <div *ngIf="!loading" class="content">
    <!-- Action Bar -->
    <div class="action-bar">
      <button mat-raised-button color="primary" (click)="openComplaintDialog()">
        <mat-icon>add</mat-icon>
        Submit New Complaint
      </button>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-grid">
      <mat-card class="stat-card pending">
        <mat-card-content>
          <div class="stat-content">
            <mat-icon>schedule</mat-icon>
            <div class="stat-info">
              <div class="stat-value">{{ getComplaintsByStatus('PENDING').length }}</div>
              <div class="stat-label">Pending</div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card reviewed">
        <mat-card-content>
          <div class="stat-content">
            <mat-icon>visibility</mat-icon>
            <div class="stat-info">
              <div class="stat-value">{{ getComplaintsByStatus('REVIEWED').length }}</div>
              <div class="stat-label">Reviewed</div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card resolved">
        <mat-card-content>
          <div class="stat-content">
            <mat-icon>check_circle</mat-icon>
            <div class="stat-info">
              <div class="stat-value">{{ getComplaintsByStatus('RESOLVED').length }}</div>
              <div class="stat-label">Resolved</div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card total">
        <mat-card-content>
          <div class="stat-content">
            <mat-icon>list</mat-icon>
            <div class="stat-info">
              <div class="stat-value">{{ complaints.length }}</div>
              <div class="stat-label">Total</div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Complaints Table -->
    <mat-card class="complaints-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>list</mat-icon>
          My Complaints
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div *ngIf="complaints.length === 0" class="no-complaints">
          <mat-icon>info</mat-icon>
          <p>No complaints submitted yet.</p>
          <button mat-raised-button color="primary" (click)="openComplaintDialog()">
            Submit Your First Complaint
          </button>
        </div>

        <div *ngIf="complaints.length > 0" class="complaints-table-container">
          <table mat-table [dataSource]="complaints" class="complaints-table">
            <!-- Title Column -->
            <ng-container matColumnDef="title">
              <th mat-header-cell *matHeaderCellDef>Title</th>
              <td mat-cell *matCellDef="let complaint">
                <div class="title-cell">
                  <span class="complaint-title">{{ complaint.title }}</span>
                  <span class="complaint-description">{{ complaint.description | slice:0:100 }}...</span>
                </div>
              </td>
            </ng-container>

            <!-- Status Column -->
            <ng-container matColumnDef="status">
              <th mat-header-cell *matHeaderCellDef>Status</th>
              <td mat-cell *matCellDef="let complaint">
                <mat-chip [color]="getStatusColor(complaint.status)" selected>
                  <mat-icon>{{ getStatusIcon(complaint.status) }}</mat-icon>
                  {{ complaint.status }}
                </mat-chip>
              </td>
            </ng-container>

            <!-- Created Date Column -->
            <ng-container matColumnDef="createdAt">
              <th mat-header-cell *matHeaderCellDef>Submitted</th>
              <td mat-cell *matCellDef="let complaint">
                <div class="date-cell">
                  <mat-icon>calendar_today</mat-icon>
                  {{ formatDate(complaint.createdAt || '') }}
                </div>
              </td>
            </ng-container>

            <!-- Actions Column -->
            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef>Actions</th>
              <td mat-cell *matCellDef="let complaint">
                <button mat-icon-button (click)="viewComplaint(complaint)" matTooltip="View Details">
                  <mat-icon>visibility</mat-icon>
                </button>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
          </table>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Information Card -->
    <mat-card class="info-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>info</mat-icon>
          How to Submit a Complaint
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="info-steps">
          <div class="step">
            <div class="step-number">1</div>
            <div class="step-content">
              <h4>Click "Submit New Complaint"</h4>
              <p>Use the button above to open the complaint form</p>
            </div>
          </div>
          <div class="step">
            <div class="step-number">2</div>
            <div class="step-content">
              <h4>Fill in the Details</h4>
              <p>Provide a clear title and detailed description of your concern</p>
            </div>
          </div>
          <div class="step">
            <div class="step-number">3</div>
            <div class="step-content">
              <h4>Select Related Grade/Subject</h4>
              <p>Optionally link your complaint to a specific grade or subject</p>
            </div>
          </div>
          <div class="step">
            <div class="step-number">4</div>
            <div class="step-content">
              <h4>Track Progress</h4>
              <p>Monitor the status of your complaint in the table above</p>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
