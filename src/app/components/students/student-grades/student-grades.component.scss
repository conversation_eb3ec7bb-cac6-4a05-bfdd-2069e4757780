.student-grades-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
  text-align: center;

  h1 {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin: 0 0 10px 0;
    color: #1976d2;
    font-size: 2.5rem;
    font-weight: 300;

    mat-icon {
      font-size: 2.5rem;
      width: 2.5rem;
      height: 2.5rem;
    }
  }

  .page-description {
    color: #666;
    font-size: 1.1rem;
    margin: 0;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  gap: 20px;

  p {
    color: #666;
    font-size: 1.1rem;
  }
}

.content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.student-info-card {
  .student-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 10px;

    .detail-item {
      display: flex;
      flex-direction: column;
      gap: 5px;

      .label {
        font-weight: 500;
        color: #666;
        font-size: 0.9rem;
      }

      .value {
        font-size: 1.1rem;
        color: #333;

        &.average-score {
          font-weight: 600;
          font-size: 1.3rem;
        }
      }
    }
  }
}

.grades-card {
  .no-grades {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40px;
    color: #666;

    mat-icon {
      font-size: 3rem;
      width: 3rem;
      height: 3rem;
      margin-bottom: 10px;
      opacity: 0.5;
    }

    p {
      font-size: 1.1rem;
      margin: 0;
    }
  }

  .grades-table-container {
    overflow-x: auto;
    margin-top: 10px;

    .grades-table {
      width: 100%;
      
      .subject-cell {
        display: flex;
        align-items: center;
        gap: 8px;

        mat-icon {
          color: #1976d2;
          font-size: 1.2rem;
          width: 1.2rem;
          height: 1.2rem;
        }
      }

      .grade-value {
        font-weight: 600;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 1.1rem;
      }

      .coefficient {
        font-weight: 500;
        color: #666;
      }
    }
  }
}

.statistics-card {
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
    margin-top: 10px;

    .stat-item {
      text-align: center;
      padding: 15px;
      background: #f5f5f5;
      border-radius: 8px;

      .stat-value {
        font-size: 2rem;
        font-weight: 600;
        color: #1976d2;
        margin-bottom: 5px;
      }

      .stat-label {
        font-size: 0.9rem;
        color: #666;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }
  }
}

// Grade color classes
.excellent {
  background-color: #4caf50;
  color: white;
}

.good {
  background-color: #8bc34a;
  color: white;
}

.average {
  background-color: #ff9800;
  color: white;
}

.passing {
  background-color: #ffc107;
  color: #333;
}

.failing {
  background-color: #f44336;
  color: white;
}

// Responsive design
@media (max-width: 768px) {
  .student-grades-container {
    padding: 15px;
  }

  .page-header h1 {
    font-size: 2rem;

    mat-icon {
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
    }
  }

  .student-details {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
