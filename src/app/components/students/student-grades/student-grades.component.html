<div class="student-grades-container">
  <div class="page-header">
    <h1>
      <mat-icon>grade</mat-icon>
      My Grades
    </h1>
    <p class="page-description">View your academic performance and grades</p>
  </div>

  <div *ngIf="loading" class="loading-container">
    <mat-spinner></mat-spinner>
    <p>Loading your grades...</p>
  </div>

  <div *ngIf="!loading" class="content">
    <!-- Student Info Card -->
    <mat-card class="student-info-card" *ngIf="student">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>person</mat-icon>
          Student Information
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="student-details">
          <div class="detail-item">
            <span class="label">Name:</span>
            <span class="value">{{ student.prenom }} {{ student.nom }}</span>
          </div>
          <div class="detail-item">
            <span class="label">Email:</span>
            <span class="value">{{ student.email }}</span>
          </div>
          <div class="detail-item">
            <span class="label">Overall Average:</span>
            <span class="value average-score" [ngClass]="getGradeColor(average)">
              {{ average | number:'1.2-2' }}/20
            </span>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Grades Table -->
    <mat-card class="grades-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>assessment</mat-icon>
          Grade Details
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div *ngIf="grades.length === 0" class="no-grades">
          <mat-icon>info</mat-icon>
          <p>No grades available yet.</p>
        </div>

        <div *ngIf="grades.length > 0" class="grades-table-container">
          <table mat-table [dataSource]="grades" class="grades-table">
            <!-- Subject Column -->
            <ng-container matColumnDef="matiere">
              <th mat-header-cell *matHeaderCellDef>Subject</th>
              <td mat-cell *matCellDef="let grade">
                <div class="subject-cell">
                  <mat-icon>book</mat-icon>
                  {{ getSubjectName(grade.matiere?.id || 0) }}
                </div>
              </td>
            </ng-container>

            <!-- Grade Column -->
            <ng-container matColumnDef="valeur">
              <th mat-header-cell *matHeaderCellDef>Grade</th>
              <td mat-cell *matCellDef="let grade">
                <span class="grade-value" [ngClass]="getGradeColor(grade.valeur)">
                  {{ grade.valeur }}/20
                </span>
              </td>
            </ng-container>

            <!-- Coefficient Column -->
            <ng-container matColumnDef="coefficient">
              <th mat-header-cell *matHeaderCellDef>Coefficient</th>
              <td mat-cell *matCellDef="let grade">
                <span class="coefficient">
                  {{ getSubjectCoefficient(grade.matiere?.id || 0) }}
                </span>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
          </table>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Statistics Card -->
    <mat-card class="statistics-card" *ngIf="grades.length > 0">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>analytics</mat-icon>
          Statistics
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value">{{ grades.length }}</div>
            <div class="stat-label">Total Grades</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ average | number:'1.2-2' }}</div>
            <div class="stat-label">Average</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ (grades | max:'valeur') || 0 }}</div>
            <div class="stat-label">Highest Grade</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ (grades | min:'valeur') || 0 }}</div>
            <div class="stat-label">Lowest Grade</div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
