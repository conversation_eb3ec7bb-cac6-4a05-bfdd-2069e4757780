.student-detail-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.detail-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 30px;
  
  h1 {
    margin: 0;
    color: var(--text-primary);
    font-size: 28px;
    font-weight: 500;
  }
  
  .back-button {
    color: var(--primary-color);
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  
  p {
    margin-top: 16px;
    color: var(--text-secondary);
  }
}

.student-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.student-info-card {
  .mat-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
      
      mat-icon {
        color: var(--primary-color);
      }
    }
  }
}

.student-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 16px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 8px;
  
  mat-icon {
    color: var(--primary-color);
    font-size: 20px;
    width: 20px;
    height: 20px;
  }
  
  .detail-content {
    display: flex;
    flex-direction: column;
    gap: 2px;
    
    .label {
      font-size: 12px;
      color: var(--text-secondary);
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    
    .value {
      font-size: 16px;
      color: var(--text-primary);
      font-weight: 500;
    }
  }
}

.average-card {
  background: linear-gradient(135deg, var(--primary-color), #5c6bc0);
  color: white;
  
  .average-display {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 8px;
    
    mat-icon {
      font-size: 32px;
      width: 32px;
      height: 32px;
    }
    
    .average-content {
      display: flex;
      flex-direction: column;
      gap: 4px;
      
      .average-label {
        font-size: 14px;
        opacity: 0.9;
      }
      
      .average-value {
        font-size: 24px;
        font-weight: 600;
        padding: 4px 12px;
        border-radius: 12px;
        background-color: rgba(255, 255, 255, 0.2);
      }
    }
  }
}

.grades-card {
  .mat-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
      
      mat-icon {
        color: var(--accent-color);
      }
    }
  }
}

.no-grades {
  text-align: center;
  padding: 40px 20px;
  
  mat-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
    color: var(--text-secondary);
    margin-bottom: 16px;
  }
  
  h3 {
    margin: 0 0 8px 0;
    color: var(--text-primary);
  }
  
  p {
    margin: 0 0 24px 0;
    color: var(--text-secondary);
  }
}

.grades-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 16px;
}

.grade-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  transition: box-shadow 0.2s ease;
  
  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.grade-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
}

.subject-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: var(--text-primary);
  
  mat-icon {
    color: var(--accent-color);
    font-size: 18px;
    width: 18px;
    height: 18px;
  }
  
  .coefficient {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: normal;
  }
}

.grade-value {
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 14px;
  display: inline-block;
  
  &.excellent {
    background-color: #e8f5e8;
    color: #2e7d32;
  }
  
  &.good {
    background-color: #e3f2fd;
    color: #1565c0;
  }
  
  &.average {
    background-color: #fff3e0;
    color: #ef6c00;
  }
  
  &.poor {
    background-color: #ffebee;
    color: #c62828;
  }
}

.grade-actions {
  display: flex;
  gap: 4px;
}

@media (max-width: 768px) {
  .student-detail-container {
    padding: 10px;
  }
  
  .detail-header h1 {
    font-size: 24px;
  }
  
  .student-details {
    gap: 12px;
  }
  
  .detail-item {
    padding: 8px;
  }
  
  .grade-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .grade-actions {
    align-self: flex-end;
  }
  
  .average-display {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }
}
