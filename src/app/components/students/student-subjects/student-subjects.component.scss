.student-subjects-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
  text-align: center;

  h1 {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin: 0 0 10px 0;
    color: #1976d2;
    font-size: 2.5rem;
    font-weight: 300;

    mat-icon {
      font-size: 2.5rem;
      width: 2.5rem;
      height: 2.5rem;
    }
  }

  .page-description {
    color: #666;
    font-size: 1.1rem;
    margin: 0;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  gap: 20px;

  p {
    color: #666;
    font-size: 1.1rem;
  }
}

.content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.summary-card {
  .summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 10px;

    .stat-item {
      text-align: center;
      padding: 20px;
      background: #f5f5f5;
      border-radius: 8px;

      .stat-value {
        font-size: 2.5rem;
        font-weight: 600;
        color: #1976d2;
        margin-bottom: 5px;
      }

      .stat-label {
        font-size: 1rem;
        color: #666;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }
  }
}

.subjects-card {
  .no-subjects {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40px;
    color: #666;

    mat-icon {
      font-size: 3rem;
      width: 3rem;
      height: 3rem;
      margin-bottom: 10px;
      opacity: 0.5;
    }

    p {
      font-size: 1.1rem;
      margin: 0;
    }
  }

  .subjects-table-container {
    overflow-x: auto;
    margin-top: 10px;

    .subjects-table {
      width: 100%;
      
      .subject-cell {
        display: flex;
        align-items: center;
        gap: 10px;

        mat-icon {
          color: #1976d2;
          font-size: 1.3rem;
          width: 1.3rem;
          height: 1.3rem;
        }

        .subject-name {
          font-weight: 500;
          font-size: 1.1rem;
        }
      }

      .coefficient-cell {
        display: flex;
        align-items: center;
        gap: 15px;

        .coefficient-value {
          font-weight: 600;
          font-size: 1.1rem;
          color: #1976d2;
          min-width: 30px;
        }

        .coefficient-bar {
          flex: 1;
          height: 8px;
          background: #e0e0e0;
          border-radius: 4px;
          overflow: hidden;
          min-width: 100px;

          .coefficient-fill {
            height: 100%;
            background: linear-gradient(90deg, #1976d2, #42a5f5);
            transition: width 0.3s ease;
          }
        }

        .coefficient-percentage {
          font-size: 0.9rem;
          color: #666;
          min-width: 50px;
          text-align: right;
        }
      }
    }
  }
}

.subjects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;

  .subject-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    mat-card-header {
      mat-card-title {
        display: flex;
        align-items: center;
        gap: 10px;
        color: #1976d2;

        mat-icon {
          font-size: 1.5rem;
          width: 1.5rem;
          height: 1.5rem;
        }
      }
    }

    .subject-info {
      margin: 15px 0;

      .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;

        .label {
          font-weight: 500;
          color: #666;
        }

        .value {
          font-weight: 600;
          color: #333;
        }
      }
    }

    .coefficient-visual {
      .coefficient-bar-large {
        height: 12px;
        background: #e0e0e0;
        border-radius: 6px;
        overflow: hidden;

        .coefficient-fill-large {
          height: 100%;
          background: linear-gradient(90deg, #1976d2, #42a5f5);
          transition: width 0.3s ease;
        }
      }
    }
  }
}

.info-card {
  background: #f8f9fa;
  border-left: 4px solid #1976d2;

  mat-card-content {
    p {
      margin-bottom: 15px;
      line-height: 1.6;
      color: #555;

      &:last-child {
        margin-bottom: 0;
      }

      strong {
        color: #1976d2;
        font-weight: 600;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .student-subjects-container {
    padding: 15px;
  }

  .page-header h1 {
    font-size: 2rem;

    mat-icon {
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
    }
  }

  .summary-stats {
    grid-template-columns: 1fr;
  }

  .subjects-grid {
    grid-template-columns: 1fr;
  }

  .coefficient-cell {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 10px !important;

    .coefficient-bar {
      width: 100%;
    }
  }
}
