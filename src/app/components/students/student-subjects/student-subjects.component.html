<div class="student-subjects-container">
  <div class="page-header">
    <h1>
      <mat-icon>book</mat-icon>
      Academic Subjects
    </h1>
    <p class="page-description">View all available subjects and their coefficients</p>
  </div>

  <div *ngIf="loading" class="loading-container">
    <mat-spinner></mat-spinner>
    <p>Loading subjects...</p>
  </div>

  <div *ngIf="!loading" class="content">
    <!-- Summary Card -->
    <mat-card class="summary-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>analytics</mat-icon>
          Summary
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="summary-stats">
          <div class="stat-item">
            <div class="stat-value">{{ subjects.length }}</div>
            <div class="stat-label">Total Subjects</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ getTotalCoefficients() }}</div>
            <div class="stat-label">Total Coefficients</div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Subjects Table -->
    <mat-card class="subjects-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>list</mat-icon>
          Subject Details
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div *ngIf="subjects.length === 0" class="no-subjects">
          <mat-icon>info</mat-icon>
          <p>No subjects available.</p>
        </div>

        <div *ngIf="subjects.length > 0" class="subjects-table-container">
          <table mat-table [dataSource]="subjects" class="subjects-table">
            <!-- Subject Name Column -->
            <ng-container matColumnDef="nom">
              <th mat-header-cell *matHeaderCellDef>Subject Name</th>
              <td mat-cell *matCellDef="let subject">
                <div class="subject-cell">
                  <mat-icon>book</mat-icon>
                  <span class="subject-name">{{ subject.nom }}</span>
                </div>
              </td>
            </ng-container>

            <!-- Coefficient Column -->
            <ng-container matColumnDef="coefficient">
              <th mat-header-cell *matHeaderCellDef>Coefficient</th>
              <td mat-cell *matCellDef="let subject">
                <div class="coefficient-cell">
                  <span class="coefficient-value">{{ subject.coefficient }}</span>
                  <div class="coefficient-bar">
                    <div 
                      class="coefficient-fill" 
                      [style.width.%]="getCoefficientPercentage(subject.coefficient)">
                    </div>
                  </div>
                  <span class="coefficient-percentage">
                    {{ getCoefficientPercentage(subject.coefficient) | number:'1.1-1' }}%
                  </span>
                </div>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
          </table>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Subject Cards (Alternative View) -->
    <div class="subjects-grid" *ngIf="subjects.length > 0">
      <mat-card class="subject-card" *ngFor="let subject of subjects">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>book</mat-icon>
            {{ subject.nom }}
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="subject-info">
            <div class="info-item">
              <span class="label">Coefficient:</span>
              <span class="value">{{ subject.coefficient }}</span>
            </div>
            <div class="info-item">
              <span class="label">Weight:</span>
              <span class="value">{{ getCoefficientPercentage(subject.coefficient) | number:'1.1-1' }}%</span>
            </div>
          </div>
          <div class="coefficient-visual">
            <div class="coefficient-bar-large">
              <div 
                class="coefficient-fill-large" 
                [style.width.%]="getCoefficientPercentage(subject.coefficient)">
              </div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Information Card -->
    <mat-card class="info-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>info</mat-icon>
          About Coefficients
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <p>
          Coefficients determine the weight of each subject in your overall average calculation. 
          Subjects with higher coefficients have more impact on your final grade.
        </p>
        <p>
          Your final average is calculated as: 
          <strong>(Sum of Grade × Coefficient) ÷ (Sum of Coefficients)</strong>
        </p>
      </mat-card-content>
    </mat-card>
  </div>
</div>
