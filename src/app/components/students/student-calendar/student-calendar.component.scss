.student-calendar {
  padding: 24px;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    h1 {
      margin: 0;
      color: #333;
    }

    .calendar-controls {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .content {
    .calendar {
      .calendar-header {
        display: flex;
        border-bottom: 1px solid #e0e0e0;
        background-color: #f5f5f5;

        .time-column {
          width: 80px;
          min-width: 80px;
          border-right: 1px solid #e0e0e0;

          .time-header {
            padding: 12px;
            font-weight: 500;
            text-align: center;
          }
        }

        .day-column {
          flex: 1;
          border-right: 1px solid #e0e0e0;

          &:last-child {
            border-right: none;
          }

          .day-header {
            padding: 12px;
            text-align: center;

            .day-name {
              font-weight: 500;
              font-size: 14px;
              color: #666;
            }

            .day-date {
              font-size: 18px;
              font-weight: 600;
              color: #333;
              margin-top: 4px;
            }
          }
        }
      }

      .calendar-body {
        display: flex;
        height: 600px;
        overflow-y: auto;

        .time-slots {
          width: 80px;
          min-width: 80px;
          border-right: 1px solid #e0e0e0;

          .time-slot {
            height: 50px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;

            .time-label {
              font-size: 12px;
              color: #666;
            }
          }
        }

        .calendar-grid {
          display: flex;
          flex: 1;

          .day-column {
            flex: 1;
            border-right: 1px solid #e0e0e0;
            position: relative;

            &:last-child {
              border-right: none;
            }

            .day-events {
              position: relative;
              height: 100%;

              .calendar-event {
                position: absolute;
                left: 2px;
                right: 2px;
                padding: 4px 8px;
                border-radius: 4px;
                color: white;
                font-size: 12px;
                cursor: pointer;
                overflow: hidden;
                transition: all 0.2s ease;

                &:hover {
                  transform: scale(1.02);
                  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
                }

                &.exam-event {
                  background-color: #f44336;
                }

                &.class-event {
                  background-color: #2196f3;
                }

                .event-title {
                  font-weight: 500;
                  margin-bottom: 2px;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }

                .event-time {
                  font-size: 10px;
                  opacity: 0.9;
                  margin-bottom: 2px;
                }

                .event-location {
                  font-size: 10px;
                  opacity: 0.8;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
              }
            }
          }
        }
      }
    }

    .loading {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 40px;

      mat-spinner {
        margin-bottom: 16px;
      }

      p {
        color: #666;
        margin: 0;
      }
    }
  }

  .event-details-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;

    .event-details {
      background-color: white;
      border-radius: 8px;
      padding: 24px;
      max-width: 400px;
      width: 90%;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);

      .event-details-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        h3 {
          margin: 0;
          color: #333;
        }
      }

      .event-details-content {
        .detail-row {
          display: flex;
          align-items: center;
          margin-bottom: 12px;

          mat-icon {
            margin-right: 12px;
            color: #666;
            font-size: 20px;
            width: 20px;
            height: 20px;
          }

          span {
            color: #333;
            font-size: 14px;
          }
        }
      }
    }
  }
} 