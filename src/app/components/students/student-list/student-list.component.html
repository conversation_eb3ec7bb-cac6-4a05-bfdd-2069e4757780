<div class="student-list-container">
  <div class="student-list-header">
    <h1>Students</h1>
    <p>Manage student information</p>
  </div>

  <mat-card>
    <mat-card-header>
      <mat-card-title>
        <mat-icon>people</mat-icon>
        Student List
      </mat-card-title>
    </mat-card-header>
    
    <mat-card-content>
      <div class="table-controls">
        <mat-form-field appearance="outline">
          <mat-label>Filter</mat-label>
          <input matInput (keyup)="applyFilter($event)" placeholder="Search students...">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>
        
        <button mat-raised-button color="primary" (click)="addStudent()">
          <mat-icon>add</mat-icon>
          Add Student
        </button>
      </div>

      <div class="loading-container" *ngIf="loading">
        <mat-spinner diameter="50"></mat-spinner>
        <p>Loading students...</p>
      </div>

      <div class="table-container" *ngIf="!loading">
        <table mat-table [dataSource]="dataSource" matSort>
          <ng-container matColumnDef="id">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>ID</th>
            <td mat-cell *matCellDef="let student">{{ student.id }}</td>
          </ng-container>

          <ng-container matColumnDef="nom">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Last Name</th>
            <td mat-cell *matCellDef="let student">{{ student.nom }}</td>
          </ng-container>

          <ng-container matColumnDef="prenom">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>First Name</th>
            <td mat-cell *matCellDef="let student">{{ student.prenom }}</td>
          </ng-container>

          <ng-container matColumnDef="email">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Email</th>
            <td mat-cell *matCellDef="let student">{{ student.email }}</td>
          </ng-container>

            <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let student">
                    <div class="action-buttons">
                        <button mat-icon-button color="primary" (click)="viewStudent(student)" matTooltip="View">
                            <mat-icon>visibility</mat-icon>
                        </button>
                        <button mat-icon-button color="accent" (click)="editStudent(student)" matTooltip="Edit">
                            <mat-icon>edit</mat-icon>
                        </button>
                        <button mat-icon-button color="warn" (click)="deleteStudent(student)" matTooltip="Delete">
                            <mat-icon>delete</mat-icon>
                        </button>
                    </div>
                </td>
            </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>

        <mat-paginator [pageSizeOptions]="[5, 10, 25, 100]" aria-label="Select page of students"></mat-paginator>
      </div>
    </mat-card-content>
  </mat-card>
</div> 