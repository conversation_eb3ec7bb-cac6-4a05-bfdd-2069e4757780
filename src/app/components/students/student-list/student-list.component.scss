.student-list-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.student-list-header {
  text-align: center;
  margin-bottom: 40px;
}

.student-list-header h1 {
  margin: 0 0 8px 0;
  color: var(--text-primary);
  font-size: 32px;
  font-weight: 500;
}

.student-list-header p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 16px;
}

.table-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  gap: 16px;
}

.table-controls mat-form-field {
  flex: 1;
  max-width: 300px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.loading-container p {
  margin-top: 16px;
  color: var(--text-secondary);
}

.table-container {
  overflow-x: auto;
}

table {
  width: 100%;
}

.mat-column-actions {
  width: 120px;
  text-align: center;
}

.action-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4px;

  button {
    mat-icon {
      font-size: 1.1rem;
      width: 1.1rem;
      height: 1.1rem;
    }
  }
}

@media (max-width: 768px) {
  .student-list-container {
    padding: 10px;
  }
  
  .student-list-header h1 {
    font-size: 24px;
  }
  
  .table-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .table-controls mat-form-field {
    max-width: none;
  }
} 