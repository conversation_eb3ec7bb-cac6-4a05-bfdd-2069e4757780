.not-found-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.not-found-content {
  background: white;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  padding: 60px 40px;
  text-align: center;
  max-width: 500px;
  width: 100%;
}

.not-found-icon {
  font-size: 80px;
  width: 80px;
  height: 80px;
  color: var(--warn-color);
  margin-bottom: 20px;
}

.not-found-content h1 {
  margin: 0 0 16px 0;
  font-size: 72px;
  font-weight: 300;
  color: var(--text-primary);
}

.not-found-content h2 {
  margin: 0 0 16px 0;
  font-size: 24px;
  font-weight: 500;
  color: var(--text-primary);
}

.not-found-content p {
  margin: 0 0 32px 0;
  color: var(--text-secondary);
  font-size: 16px;
  line-height: 1.5;
}

.not-found-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.not-found-actions button {
  min-width: 120px;
}

@media (max-width: 480px) {
  .not-found-content {
    padding: 40px 20px;
  }
  
  .not-found-content h1 {
    font-size: 48px;
  }
  
  .not-found-content h2 {
    font-size: 20px;
  }
  
  .not-found-actions {
    flex-direction: column;
    align-items: center;
  }
} 